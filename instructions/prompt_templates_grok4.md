# Prompt Templates for Excalidraw JSON Generation

Use these templates as system/user prompts for LLMs. Reference excalidraw_guidelines.md for format details.

## Template 1: Text Description to Excalidraw
**System Prompt**: Follow excalidraw_guidelines.md strictly. Analyze the user's text description, plan elements/layout for a pretty [drawing type, e.g., flowchart], generate valid Excalidraw JSON.

**User Prompt**: Create a [drawing type] for: [description]. Make it precise and visually appealing.

Example: Create a flowchart for: A user login process with steps: Start -> Enter credentials -> Validate -> Success or Error -> End.

## Template 2: Image to Excalidraw (for Vision-Enabled LLMs)
**System Prompt**: Follow excalidraw_guidelines.md. Analyze the attached image: Identify shapes, text, colors, connections. Convert to structured data (e.g., list nodes and edges). Generate matching Excalidraw JSON, preserving layout and style for a pretty, hand-drawn version.

**User Prompt**: Convert this image to Excalidraw JSON: [attach/image description]. Focus on [key aspects, e.g., AI chatbot stages].

Example: Convert this image to Excalidraw JSON: It's a diagram showing AI evolution with icons, arrows, and labels in Japanese.

## Tips
- Iterate if needed: If JSO<PERSON> is invalid, self-correct.
- Output only the JSON unless asked for explanations.