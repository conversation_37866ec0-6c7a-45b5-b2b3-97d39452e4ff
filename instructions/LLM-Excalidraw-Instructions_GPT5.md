# LLM Excalidraw Authoring Instructions (v1.0)

Goal: Given a textual description (and optionally an image), output a single .excalidraw JSON scene that opens without error in Excalidraw, uses consistent styling, and is neatly laid out.

Output contract:
- Output only a single JSON object.
- Root keys: `type`, `version`, `source`, `elements`, `appState`, `files`.
- `type` must be `"excalidraw"`. `version` must be `2`. `source` is a string identifying the generator (e.g., `"llm-excalidraw-guidelines@1"`).
- All element `id`s must be unique; all references (`containerId`, bindings, `fileId`) must exist.

Scene JSON skeleton:
{
  "type": "excalidraw",
  "version": 2,
  "source": "llm-excalidraw-guidelines@1",
  "elements": [],
  "appState": {
    "theme": "light",
    "viewBackgroundColor": "#ffffff",
    "gridSize": null,
    "scrollX": 0,
    "scrollY": 0,
    "zoom": { "value": 1 },
    "currentItemStrokeColor": "#1f2937",
    "currentItemBackgroundColor": "#a7f3d0",
    "currentItemFillStyle": "solid",
    "currentItemStrokeWidth": 2,
    "currentItemRoughness": 1,
    "currentItemOpacity": 100
  },
  "files": {}
}

Supported element types to use:
- Shapes: `"rectangle"`, `"ellipse"`, `"diamond"`, `"line"`, `"arrow"`, `"freedraw"`, `"text"`, `"image"`, `"frame"`.
- Prefer shapes + arrows + text. Use `image` only if you must embed a bitmap.

Common element fields (all elements):
- `id`: string (unique).
- `type`: one of the types above.
- `x`, `y`: number (top-left coordinates).
- `width`, `height`: number.
- `angle`: number in radians (0 if upright).
- `strokeColor`: hex string, e.g., "#111827".
- `backgroundColor`: hex string (shapes only).
- `fillStyle`: "solid" | "hachure" | "cross-hatch".
- `strokeWidth`: number (1–4 recommended).
- `strokeStyle`: "solid" | "dashed" | "dotted".
- `roughness`: number (0–3); 0 = clean, 1 = subtle sketchy.
- `opacity`: 0–100.
- `roundness`: null or { "type": 2 } for rounded corners on rectangles/diamonds.
- `seed`: integer (random).
- `version`: integer ≥ 1 (increment if you edit the element).
- `versionNonce`: integer (random).
- `isDeleted`: false.
- `groupIds`: [] (or list of group ids).
- `boundElements`: [] (or links like { "type":"text","id":"..." }).
- `locked`: false.
- `link`: optional URL string.

Text-specific fields (for `type: "text"`):
- `text`: string (use "\n" for line breaks).
- `fontSize`: number (e.g., 20).
- `fontFamily`: 1=Virgil, 2=Helvetica, 3=Cascadia.
- `textAlign`: "left" | "center" | "right".
- `verticalAlign`: "top" | "middle" | "bottom".
- `containerId`: null or id of the shape that contains this text.
- `baseline`: number (approximate; use fontSize * 0.8 if unsure).

Line/arrow-specific fields:
- `points`: [[0,0],[dx,dy], ...] relative to the element’s `x,y`.
- `lastCommittedPoint`: null or last point (optional).
- `startBinding` / `endBinding`: null or
  {
    "elementId": "<id of target shape>",
    "gap": 4,
    "focus": 0
  }
- `startArrowhead` / `endArrowhead`: null | "arrow" | "triangle" | "bar" | "dot".

Image-specific fields:
- `fileId`: key present in root `files`.
- `scale`: [1,1] (or as needed).
- `status`: "saved".

Frame-specific fields:
- `name`: string (label shown in UI).

App state fields to set:
- Use `viewBackgroundColor` for canvas color.
- `gridSize`: null (off) or a number like 20.
- `zoom.value`: 1 by default; adjust to fit large scenes.
- `scrollX`, `scrollY`: set so that elements are visible (top-left of diagram around 0,0 is safe).

Styling system (use consistently):
- Palette:
  - Ink: `#111827`
  - Primary fill: `#a7f3d0`
  - Secondary fill: `#bfdbfe`
  - Accent fill: `#fde68a`
  - Muted fill: `#e5e7eb`
- Text color = Ink.
- `strokeWidth`: 2 for shapes, 2–3 for main connectors.
- `roughness`: 1 (clean hand-drawn).
- `fillStyle`: "solid".
- `strokeStyle`: "solid".
- `roundness`: `{ "type": 2 }` for boxes and diamonds.

Layout rules:
- Base grid: 16 px; snap positions and sizes to multiples of 8 or 16.
- Spacing:
  - 32 px between sibling nodes.
  - 48–64 px between levels (e.g., mind-map branches).
- Node sizes:
  - Title boxes: min 240×72.
  - Step boxes: min 160×64.
  - Pill shapes (ellipse) for start/end: 140×56.
- Text:
  - Titles: 28–32 pt, `fontFamily: 2`, `textAlign: "center"`.
  - Body: 18–22 pt, `fontFamily: 2`.
  - Wrap long labels with "\n" to keep width reasonable.
- Connectors:
  - Use `"arrow"` with `endArrowhead: "arrow"`.
  - Prefer straight or two-segment lines; avoid excessive kinks.
  - Bind arrows to shapes with `startBinding`/`endBinding` and `gap: 6`.

Containers with bound text (preferred):
1) Create a `rectangle` (or `diamond`/`ellipse`) element.
2) Create a `text` element whose `containerId` is the shape’s id, and place it inside (center it with `textAlign: "center", verticalAlign: "middle"`).
3) Optionally add the text element id into the shape’s `boundElements`.

Grouping:
- Use `groupIds` to keep related shapes + text together. Create a new group id string (e.g., "g-...") and put it into all member elements’ `groupIds`.

Frames:
- For sections (e.g., lanes), draw a `frame` around a group and set `name`.

Image handling (when user supplies an example picture):
- Convert the picture to a dataURL (e.g., PNG) and store in root `files` as
  "files": {
    "<fileId>": { "mimeType": "image/png", "dataURL": "data:image/png;base64,..." }
  }
- Then create an `image` element with matching `fileId`, sized appropriately.

Validation checklist (LLM must self-check before finalizing):
- All ids unique; all references (`containerId`, `startBinding.elementId`, `endBinding.elementId`, `fileId`) resolve.
- No elements have negative width or height.
- All `opacity` between 0–100, `roughness` 0–3.
- At least one element present and visible near the origin (0,0).
- If any `image` exists, `files[fileId]` exists with a plausible `dataURL`.

Authoring process (internal steps the LLM should follow):
1) Parse the user request (and image, if provided) into a scene plan: nodes, edges, groups, lanes/frames.
2) Assign coordinates on a 16 px grid, left-to-right or top-to-bottom depending on diagram type.
3) Create shapes with bound text using the styling system.
4) Create arrows with bindings to the nearest connection sides.
5) Add frames for sections/layers if needed; add subtle caption text.
6) Run the validation checklist and fix issues.
7) Emit the final JSON (no commentary).

Diagram patterns (use these heuristics):
- Flowchart: Start/end = ellipse; step = rectangle; decision = diamond; connectors = arrows; align in columns; keep 48 px vertical gaps.
- Mind‑map: Central rounded rectangle or ellipse; first‑level branches radiate evenly; second‑level branches offset with alternating vertical positions.
- Swimlanes: Use `frame` per lane; put a lane label as a `text` bound to the frame (not container-bound).
- Architecture diagram: Group related services with `frame`s; use rectangles for services, diamonds for decision points in flows; add icons as `image` only if supplied.

Notes:
- This format evolves; keep the structure minimal and stable (fields above).