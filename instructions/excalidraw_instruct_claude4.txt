## File 1: Main Excalidraw Generation Guide

```markdown
# Excalidraw JSON Generation Guide for LLMs
Version: 2.0
Last Updated: 2025-01-21

## CRITICAL REQUIREMENTS

### 1. JSON Structure Validation
ALWAYS generate valid Excalidraw JSON with this exact structure:

```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://excalidraw.com",
  "elements": [
    // Array of drawing elements
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```

### 2. Element Types and Properties

#### Rectangle
```json
{
  "type": "rectangle",
  "version": 1,
  "versionNonce": 123456789,
  "index": "a0",
  "isDeleted": false,
  "id": "unique-id-rect",
  "fillStyle": "solid",
  "strokeWidth": 2,
  "strokeStyle": "solid",
  "roughness": 1,
  "opacity": 100,
  "angle": 0,
  "x": 100,
  "y": 100,
  "strokeColor": "#1e1e1e",
  "backgroundColor": "#ffffff",
  "width": 200,
  "height": 100,
  "seed": 1234567890,
  "groupIds": [],
  "frameId": null,
  "roundness": {
    "type": 3,
    "value": 8
  },
  "boundElements": [],
  "updated": 1,
  "link": null,
  "locked": false
}
```

#### Text Element
```json
{
  "type": "text",
  "version": 1,
  "versionNonce": 123456789,
  "index": "a1",
  "isDeleted": false,
  "id": "unique-id-text",
  "fillStyle": "solid",
  "strokeWidth": 2,
  "strokeStyle": "solid",
  "roughness": 1,
  "opacity": 100,
  "angle": 0,
  "x": 150,
  "y": 130,
  "strokeColor": "#1e1e1e",
  "backgroundColor": "transparent",
  "width": 100,
  "height": 25,
  "seed": 1234567890,
  "groupIds": [],
  "frameId": null,
  "roundness": null,
  "boundElements": [],
  "updated": 1,
  "link": null,
  "locked": false,
  "fontSize": 16,
  "fontFamily": 1,
  "text": "Sample Text",
  "textAlign": "center",
  "verticalAlign": "middle",
  "containerId": null,
  "originalText": "Sample Text",
  "autoResize": true,
  "lineHeight": 1.25
}
```

#### Arrow
```json
{
  "type": "arrow",
  "version": 1,
  "versionNonce": 123456789,
  "index": "a2",
  "isDeleted": false,
  "id": "unique-id-arrow",
  "fillStyle": "solid",
  "strokeWidth": 2,
  "strokeStyle": "solid",
  "roughness": 1,
  "opacity": 100,
  "angle": 0,
  "x": 300,
  "y": 150,
  "strokeColor": "#1e1e1e",
  "backgroundColor": "transparent",
  "width": 100,
  "height": 0,
  "seed": 1234567890,
  "groupIds": [],
  "frameId": null,
  "roundness": {
    "type": 2
  },
  "boundElements": [],
  "updated": 1,
  "link": null,
  "locked": false,
  "startBinding": null,
  "endBinding": null,
  "lastCommittedPoint": null,
  "startArrowhead": null,
  "endArrowhead": "arrow",
  "points": [[0, 0], [100, 0]]
}
```

### 3. Color Standards
Use these predefined color palettes:

#### Primary Colors
- **Green**: #16a34a (Success/Start)
- **Blue**: #2563eb (Process/Info)
- **Yellow**: #eab308 (Warning/In Progress)
- **Red**: #dc2626 (Error/Stop)
- **Purple**: #9333ea (Final/Complete)
- **Orange**: #ea580c (Decision/Branch)

#### Background Colors (Lighter variants)
- **Light Green**: #dcfce7
- **Light Blue**: #dbeafe
- **Light Yellow**: #fef3c7
- **Light Red**: #fecaca
- **Light Purple**: #ede9fe
- **Light Orange**: #fed7aa

### 4. Positioning Rules

#### Grid System
- Use 20px grid increments
- Minimum spacing between elements: 40px
- Standard element sizes:
  - Small boxes: 120x60px
  - Medium boxes: 200x100px
  - Large boxes: 300x150px

#### Layout Patterns
- **Horizontal Flow**: Left to right, y-coordinates aligned
- **Vertical Flow**: Top to bottom, x-coordinates aligned
- **Hierarchical**: Center main node, branches radiating outward

### 5. Text Guidelines
- **Font Family**: 1 (Virgil - hand-drawn style)
- **Font Sizes**: 
  - Headers: 20px
  - Body text: 16px
  - Annotations: 12px
- **Alignment**: Center for boxes, left for annotations
- **Line Height**: 1.25

### 6. Arrow Connection Rules
- Arrows should start/end at box edges
- Use `startBinding` and `endBinding` for connected arrows
- Arrow types:
  - Solid arrows for main flow
  - Dashed arrows for optional/conditional paths

## GENERATION PROCESS

### Step 1: Analyze Input
1. Identify diagram type (flowchart, mind map, system architecture, etc.)
2. Extract key elements and relationships
3. Determine layout direction and hierarchy

### Step 2: Plan Layout
1. Calculate canvas size needed
2. Position main elements using grid system
3. Plan connection paths
4. Assign colors based on element types

### Step 3: Generate Elements
1. Create shapes first (rectangles, ellipses, diamonds)
2. Add text elements for each shape
3. Create arrows/connections
4. Add annotations and labels

### Step 4: Validate Output
1. Ensure all IDs are unique
2. Check JSON syntax validity
3. Verify color consistency
4. Confirm proper spacing and alignment

## QUALITY STANDARDS
- All elements must be properly aligned
- Color usage must be consistent and meaningful
- Text must be readable and appropriately sized
- Arrows must properly connect elements
- Overall layout must be balanced and professional
```

## File 2: Workflow Diagram Template

```markdown
# Workflow Diagram Generation Template
Specialized for process flows and development workflows

## Template Structure

### Standard Workflow Elements
1. **Start/Input**: Green rounded rectangle
2. **Process**: Blue rectangle
3. **Decision**: Orange diamond
4. **Output**: Red/Pink rectangle
5. **End/Result**: Purple rectangle

### Example Workflow JSON Template

```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://excalidraw.com",
  "elements": [
    {
      "type": "rectangle",
      "id": "start-box",
      "x": 100, "y": 100,
      "width": 120, "height": 60,
      "strokeColor": "#16a34a",
      "backgroundColor": "#dcfce7",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "roughness": 1,
      "opacity": 100,
      "roundness": {"type": 3, "value": 8}
    },
    {
      "type": "text",
      "id": "start-text",
      "x": 130, "y": 120,
      "width": 60, "height": 20,
      "text": "开始",
      "fontSize": 16,
      "fontFamily": 1,
      "textAlign": "center",
      "strokeColor": "#16a34a"
    },
    {
      "type": "arrow",
      "id": "arrow-1",
      "x": 220, "y": 130,
      "width": 80, "height": 0,
      "strokeColor": "#1e1e1e",
      "strokeWidth": 2,
      "points": [[0, 0], [80, 0]],
      "endArrowhead": "arrow"
    }
  ]
}
```

### Multi-Section Workflow Pattern
For complex workflows like the AI development example:

1. **Section Headers**: Large text at left margin
2. **Horizontal Sections**: Separated by 150px vertical spacing
3. **Icons**: 40x40px squares for people/robots
4. **Annotations**: Small text below main flow

### Chinese Text Support
- Use UTF-8 encoding for Chinese characters
- Keep text concise (2-4 characters per box)
- Use consistent terminology across sections

### Color Coding for AI Workflows
- **Traditional/Manual**: Green
- **AI-Assisted**: Yellow/Orange
- **Review/QA**: Pink/Red
- **Final Output**: Purple
- **Iterations**: Dashed arrows
```

## File 3: Quick Reference Guide

```markdown
# Excalidraw JSON Quick Reference

## Essential Properties Checklist
- [ ] `type`: "rectangle"|"text"|"arrow"|"ellipse"|"diamond"
- [ ] `id`: Unique string identifier
- [ ] `x`, `y`: Position coordinates
- [ ] `width`, `height`: Dimensions
- [ ] `strokeColor`: Border color (hex)
- [ ] `backgroundColor`: Fill color (hex)
- [ ] `strokeWidth`: 1-5 (usually 2)
- [ ] `opacity`: 100 (fully opaque)
- [ ] `roughness`: 1 (clean lines)

## Color Quick Pick
```json
{
  "success": "#16a34a",
  "process": "#2563eb", 
  "warning": "#eab308",
  "error": "#dc2626",
  "complete": "#9333ea",
  "light_bg": "#f8fafc"
}
```

## Common Shapes Quick Copy

### Process Box
```json
{
  "type": "rectangle",
  "x": 100, "y": 100,
  "width": 200, "height": 80,
  "strokeColor": "#2563eb",
  "backgroundColor": "#dbeafe",
  "roundness": {"type": 3, "value": 8}
}
```

### Decision Diamond
```json
{
  "type": "diamond", 
  "x": 100, "y": 100,
  "width": 120, "height": 120,
  "strokeColor": "#ea580c",
  "backgroundColor": "#fed7aa"
}
```

### Connecting Arrow
```json
{
  "type": "arrow",
  "x": 300, "y": 140,
  "width": 100, "height": 0,
  "points": [[0, 0], [100, 0]],
  "endArrowhead": "arrow"
}
```
```

## File 4: Validation Checklist

```markdown
# Excalidraw JSON Validation Checklist

## Structure Validation
- [ ] Root object has `type: "excalidraw"`
- [ ] `version: 2` is specified
- [ ] `elements` array exists
- [ ] `appState` object exists

## Element Validation
- [ ] All elements have unique `id` values
- [ ] Required properties present for each element type
- [ ] Coordinates are numbers (not strings)
- [ ] Colors are valid hex codes
- [ ] Text elements have proper `fontSize` and `fontFamily`

## Design Quality
- [ ] Elements are properly aligned
- [ ] Consistent spacing between elements
- [ ] Readable font sizes (minimum 12px)
- [ ] Appropriate color contrast
- [ ] Logical flow direction
- [ ] Professional appearance

## JSON Syntax
- [ ] Valid JSON syntax (no trailing commas)
- [ ] Properly escaped special characters
- [ ] Consistent property naming
- [ ] No undefined values

## Testing Commands
```bash
# Validate JSON syntax
cat diagram.excalidraw | jq .

# Check for required properties
cat diagram.excalidraw | jq '.elements[] | select(.id == null)'
```
```

These instruction files provide comprehensive guidance for LLMs to generate high-quality Excalidraw JSON drawings. The files are structured to be:

1. **Comprehensive** - Covering all aspects of Excalidraw JSON generation
2. **Practical** - Including working templates and examples
3. **Specific** - Tailored for the workflow diagram style shown in your example
4. **Validatable** - Including quality checks and standards

You can save these as:
- `excalidraw-generation-guide.md`
- `workflow-diagram-template.md` 
- `excalidraw-quick-reference.md`
- `validation-checklist.md`

The LLM can now use these instructions to analyze your input (text description or image) and generate precise, properly formatted Excalidraw JSON that matches your requirements.