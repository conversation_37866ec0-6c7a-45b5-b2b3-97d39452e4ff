# Excalidraw Style Guide & Component Library

This guide provides named styles and reusable components to ensure diagrams are aesthetically pleasing and consistent.

## 1. Color Palette

| Name           | Use Case                      | `backgroundColor` | `strokeColor` |
| -------------- | ----------------------------- | ----------------- | ------------- |
| `green_box`    | Initial step, simple request  | `#e9f7ef`         | `#52c41a`     |
| `yellow_box`   | Process step, product design  | `#fffbe6`         | `#faad14`     |
| `pink_box`     | Process step, development     | `#fff0f6`         | `#eb2f96`     |
| `purple_box`   | Final step, testing/ops       | `#f9f0ff`         | `#722ed1`     |
| `blue_box`     | AI component, central process | `#e6f7ff`         | `#1890ff`     |
| `grey_outline` | Alternate process, less focus | `#fafafa`         | `#8c8c8c`     |

## 2. Text Styles

| Name              | Use Case                       | `fontSize` | `fontFamily` | `strokeColor` | Notes                          |
| ----------------- | ------------------------------ | ---------- | ------------ | ------------- | ------------------------------ |
| `main_title`      | The main title of the diagram  | 36         | 1 (Virgil)   | `#1e1e1e`     | Center aligned.                |
| `section_header`  | Title for a horizontal section | 28         | 1 (Virgil)   | *Varies*      | Use a relevant color.          |
| `box_text`        | Text inside a shape            | 20         | 1 (Virgil)   | `#1e1e1e`     | Center aligned.                |
| `annotation_text` | Text labeling a flow or arrow  | 16         | 1 (Virgil)   | `#595959`     | Use near the relevant element. |

## 3. Shape & Line Styles

*   **Process Box**: A `rectangle` with `roundness: { type: 3 }` (medium roundness), `strokeWidth: 2`, and `fillStyle: "solid"`. Colors should follow the palette above.
*   **Main Flow Arrow**: An `arrow` with `strokeStyle: "dashed"`, `strokeWidth: 1`, `endArrowhead: "arrow"`.
*   **Detail Arrow**: An `arrow` with `strokeStyle: "solid"`, `strokeWidth: 1`, `endArrowhead: "arrow"`.
*   **Section Separator**: A `line` element with `strokeStyle: "dashed"` and `strokeWidth: 1`.

## 4. Component Library (Emojis/Images)

To insert an emoji, use an `image` element and reference the appropriate `fileId`.

| Component Name            | Description       | Recommended `width` / `height` | `fileId`             |
| ------------------------- | ----------------- | ------------------------------ | -------------------- |
| `component_artist_emoji`  | Product designer  | 55 / 55                        | `artist_emoji_png`   |
| `component_dev_emoji`     | Developer         | 55 / 55                        | `developer_emoji_png`|
| `component_robot_emoji`   | AI Assistant      | 60 / 60                        | `robot_emoji_png`    |
| `component_plus_symbol`   | Addition symbol   | 40 / 40                        | `plus_symbol_png`    |