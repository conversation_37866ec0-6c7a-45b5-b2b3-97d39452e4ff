# Excalidraw JSON Generation Guidelines for LLMs

## Purpose
This document provides instructions for LLMs to generate precise, pretty, and valid Excalidraw JSON files. Use this to create drawings like flowcharts, mind maps, schemes, or diagrams based on text descriptions or images. Ensure outputs are aesthetically pleasing (balanced layout, colors, hand-drawn style) and functional (openable in excalidraw.com).

## Excalidraw JSON Format Overview
Generate a JSON object with these REQUIRED keys (based on Excalidraw version 2 schema):
- `"type"`: Always "excalidraw".
- `"version"`: Integer, set to 2.
- `"source"`: String, e.g., "https://excalidraw.com" or "Generated by AI".
- `"elements"`: Array of element objects (the core drawing). Each element MUST have:
  - `"id"`: Unique string (e.g., auto-generate like "rect1").
  - `"type"`: String, e.g., "rectangle", "ellipse", "diamond", "text", "arrow", "line", "image".
  - `"x"`: Number (horizontal position, start from 0 for top-left).
  - `"y"`: Number (vertical position).
  - `"width"`: Number (>0).
  - `"height"`: Number (>0, except for lines/arrows).
  - `"angle"`: Number (rotation in radians, default 0).
  - `"strokeColor"`: String (hex, e.g., "#1e1e1e" for black).
  - `"backgroundColor"`: String (hex or "transparent").
  - `"fillStyle"`: String (e.g., "solid", "hachure").
  - `"strokeWidth"`: Number (e.g., 1-4 for thickness).
  - `"strokeStyle"`: String (e.g., "solid", "dashed").
  - `"roughness"`: Number (0-2; 1 for hand-drawn look).
  - `"opacity"`: Number (0-100).
  - For "text": Add `"text"`: String, `"fontSize"`: Number, `"fontFamily"`: Number (1-3), `"textAlign"`: String.
  - For "arrow"/"line": Add `"points"`: Array of [x,y] arrays for path; `"startBinding"`, `"endBinding"` for connections (objects with `"elementId"`).
  - For "image": Add `"fileId"`: String, and reference in "files".
- `"appState"`: Object with:
  - `"gridSize"`: Null or number (e.g., 20 for grid).
  - `"viewBackgroundColor"`: String (hex, e.g., "#ffffff").
  - `"zoom"`: Object like { "value": 1 }.
  - `"scrollX"`, `"scrollY"`: Numbers for viewport (default 0).
- `"files"`: Optional object for images (e.g., { "id": { "mimeType": "image/png", "dataURL": "base64-encoded-image" } }).

OPTIONAL: Add `"libraryItems"` for reusable shapes.

Ensure JSON is valid (no trailing commas, proper nesting). Aim for 10-50 elements max for simplicity.

## Best Practices for Precise and Pretty Drawings
- **Precision**: Match input exactly. For images, analyze elements (shapes, text, connections) using vision capabilities. For text, parse into nodes/edges.
- **Aesthetics (Make it Pretty)**:
  - Use hand-drawn style: Set `"roughness": 1`, `"fillStyle": "hachure"`.
  - Colors: Use a palette (e.g., "#a5d8ff" for blue boxes, "#ff8787" for red accents). Ensure contrast.
  - Layout: Space elements evenly (e.g., x/y in multiples of 50). Center main elements. Use groups for organization.
  - Connections: Bind arrows to shapes for dynamic resizing.
  - Text: Keep concise; auto-size with `"baseline"`, `"verticalAlign"`.
  - Variety: Mix shapes (rectangles for processes, diamonds for decisions, ellipses for nodes).
- **Types of Drawings**:
  - Flowchart: Arrows between rectangles/diamonds.
  - Mind Map: Central ellipse with radiating arrows.
  - Scheme/Diagram: Labeled boxes with lines.
- **From Images**: Identify all visual elements, relationships, and text. Convert to structured data (e.g., CSV of nodes/edges) first, then to JSON (inspired by mastra.ai workflows).
- **Validation Steps** (Always Perform):
  1. Generate draft JSON.
  2. Check for syntax errors (e.g., valid JSON.parse).
  3. Verify required keys and positive dimensions.
  4. Simulate layout: Ensure no overlaps unless intended.
  5. If invalid, iterate (e.g., "Failed after X attempts: [error]").

## Output Format
- Respond ONLY with the JSON object.
- If validation fails, explain and retry.
- Example for a simple box: { "type": "excalidraw", "version": 2, "source": "AI", "elements": [{ "id": "box1", "type": "rectangle", "x": 100, "y": 100, "width": 200, "height": 100, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "roughness": 1 }] , "appState": { "viewBackgroundColor": "#ffffff" } }