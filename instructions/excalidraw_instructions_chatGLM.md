# Excalidraw JSON Generation Instructions

## Main Instructions File (.md)

```markdown
# Excalidraw JSON Generation Guide

## Purpose
This guide helps AI models generate proper Excalidraw JSON format diagrams based on user descriptions or reference images.

## Core Principles
1. **Precision**: Follow Excalidraw's JSON structure exactly
2. **Readability**: Make diagrams clear and easy to understand
3. **Reusability**: Create elements that can be easily modified

## Excalidraw JSON Structure
Excalidraw drawings are represented as JSON with two main components:
- `elements`: Array of drawing elements (shapes, text, arrows, etc.)
- `appState`: Editor state information

```json
{
  "elements": [ ... ],
  "appState": { ... }
}
```

## Element Types and Properties
Common element types include:
- `rectangle`: Basic shape with width/height
- `ellipse`: Circular shape
- `arrow`: Connecting lines
- `text`: Text elements
- `diamond`: Diamond shape (often for decisions)

Essential properties:
- `type`: Element type (required)
- `x`, `y`: Position coordinates
- `width`, `height`: Dimensions
- `strokeColor`: Border color
- `fillColor`: Fill color
- `text`: Text content (for text elements)

## Workflow for Generation
1. **Parse User Input**: Understand if input is text description or reference image
2. **Identify Diagram Type**: Flowchart, mind-map, scheme, etc.
3. **Define Elements**: Create necessary shapes and text
4. **Position Elements**: Arrange elements logically
5. **Add Connections**: Use arrows to show relationships
6. **Finalize Formatting**: Set colors, styles, and layout

## Best Practices
- Use consistent spacing between elements
- Choose readable colors and font sizes
- Group related elements visually
- Use appropriate symbols for diagram type

## Example Templates
 Templates are available in the `templates/` folder for common diagram types.

## Output Requirements
1. Generate valid JSON
2. Include all necessary elements
3. Format for optimal display in Excalidraw
4. Add minimal appState for proper rendering

For more detailed specifications, refer to the Excalidraw documentation: [excalidraw.com/docs](https://excalidraw.com/docs)


## Template File for Common Diagrams (.txt)

```json
{
  "elements": [
    {
      "type": "rectangle",
      "x": 100,
      "y": 100,
      "width": 200,
      "height": 80,
      "strokeColor": "#000000",
      "fillColor": "#ffffff",
      "groupId": null,
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "type": "arrow",
      "x": 200,
      "y": 180,
      "strokeColor": "#000000",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "type": "text",
      "x": 150,
      "y": 140,
      "textContent": "Sample Text",
      "fontSize": 20,
      "fontFamily": 1
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": null,
    "zoom": 1
  }
}
```

## Usage Instructions for LLMs

1. **Text-Based Generation**:
   - Analyze user description to determine diagram type
   - Select appropriate template from templates folder
   - Modify template elements based on specifications
   - Ensure proper positioning and connections

2. **Image-Based Generation**:
   - Use image recognition to identify elements and structure
   - Map visual elements to Excalidraw JSON properties
   - Preserve layout and relationships from reference image

3. **Validation**:
   - Check JSON syntax
   - Verify element properties
   - Ensure logical grouping and connections

These files are designed to be downloaded and used as reference materials for AI models generating Excalidraw diagrams. The templates provide a foundation that can be customized for various diagram types and complexities.
