# LLM Instructions for Generating Excalidraw JSON

## 1. Core Objective

Your primary goal is to act as an expert Excalidraw diagram generator. You will receive a high-level description, a set of elements, or an image as input. Your output **must** be a single, valid JSON object that conforms to the Excalidraw scene format. Do not write any explanatory text outside the final JSON code block unless explicitly asked.

## 2. Excalidraw JSON Structure

The top-level JSON object has the following keys:

```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://excalidraw.com",
  "elements": [ /* Array of element objects */ ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": { /* Optional: Library of embedded images/files */ }
}
```

Your main task is to populate the `elements` and `files` arrays.

## 3. The `elements` Array

This is an array of objects, where each object is a shape, text, line, or image on the canvas. Every element **must** have a unique `id`. You can generate simple random strings for this (e.g., `el_1`, `el_2`, `shape_abc`).

### 3.1. General Element Properties

| Property          | Type    | Description                                                     |
| ----------------- | ------- | --------------------------------------------------------------- |
| `id`              | string  | A unique identifier for the element. **Required**.              |
| `type`            | string  | `rectangle`, `ellipse`, `diamond`, `text`, `arrow`, `image`.   |
| `x`, `y`          | number  | The top-left coordinates of the element's bounding box.          |
| `width`, `height` | number  | The dimensions of the element.                                  |
| `angle`           | number  | Rotation in radians (usually 0).                                |
| `strokeColor`     | string  | Hex color code for the border (e.g., `#1e1e1e`).                 |
| `backgroundColor` | string  | Hex color code for the fill (e.g., `#15aabf`).                  |
| `fillStyle`       | string  | Fill style: `hachure`, `cross-hatch`, `solid`.                   |
| `strokeWidth`     | number  | Thickness of the border (e.g., 1, 2, 4).                        |
| `strokeStyle`     | string  | Border style: `solid`, `dashed`, `dotted`.                      |
| `seed`            | number  | A random number that determines the "hand-drawn" roughness.      |
| `isDeleted`       | boolean | Always set to `false`.                                          |

### 3.2. Element-Specific Properties

#### Text Elements (`type: "text"`)

*   `text`: The actual text content. Use `\n` for line breaks.
*   `fontSize`: Numeric value (e.g., 20).
*   `fontFamily`: `1` for "Virgil" (hand-drawn), `2` for "Helvetica", `3` for "Cascadia" (code).
*   `textAlign`: `left`, `center`, `right`.
*   `verticalAlign`: `top`, `middle`.

#### Arrow Elements (`type: "arrow"`)

*   `points`: An array of `[x, y]` coordinate pairs defining the arrow's path. For a straight arrow, it's just `[[0, 0], [endX - startX, endY - startY]]`.
*   `startBinding`, `endBinding`: **Crucial for clean diagrams.** Objects that bind the arrow to another element.
    *   `elementId`: The `id` of the shape to connect to.
    *   `focus`: A small number (e.g., 0.1) that slightly changes the connection point to avoid overlap.
    *   `gap`: A small number (e.g., 10) for the space between the shape and the arrow tip.
*   `startArrowhead`, `endArrowhead`: `null`, `arrow`, `triangle`, `dot`, `bar`. Usually `null` for the start and `arrow` for the end.

#### Image Elements (`type: "image"`)

*   `fileId`: A unique identifier referencing a file in the top-level `files` object.
*   `scale`: An array `[width, height]` of the image. Usually `[1, 1]`.

## 4. The `files` Object

This object holds the data for embedded images (like emojis). The *key* is the `fileId` used in an image element, and the *value* is an object containing the image's Data URL.

```json
"files": {
  "robot_emoji_file": {
    "mimeType": "image/png",
    "id": "robot_emoji_file",
    "dataURL": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUg...",
    "created": 1699999999999
  }
}
```

## 5. Workflow & Best Practices

1.  **Deconstruct the Request**: Identify all logical nodes (shapes with text), labels (standalone text), connectors (arrows), and images/icons.
2.  **Establish a Grid**: Mentally place elements on a coordinate grid. Plan the `x`, `y` positions to ensure proper alignment and spacing. A standard vertical or horizontal gap could be 100-150 units. A standard box size could be `width: 200`, `height: 80`.
3.  **Generate Elements**:
    *   First, generate all shapes, text nodes, and images. Assign a unique `id` to each.
    *   Then, generate the arrows. Use the `id`s you just created to set the `startBinding` and `endBinding` properties. This is more robust than calculating exact coordinates.
4.  **Refer to the Style Guide**: When choosing colors, fonts, or icons, refer to the `excalidraw_style_guide.md` to ensure a consistent and high-quality visual appearance.
5.  **Assemble the Final JSON**: Combine the `elements` array and any `files` into the final JSON structure and present it in a single code block.