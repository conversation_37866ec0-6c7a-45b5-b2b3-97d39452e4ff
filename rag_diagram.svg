<svg viewBox="0 0 900 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Background container with rounded corners -->
  <rect x="10" y="10" width="880" height="480" rx="20" ry="20" fill="#e6f5f5" stroke="#5cb3b3" stroke-width="2"/>
  
  <!-- Yellow circular steps at the top -->
  <circle cx="420" y="30" r="20" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5" stroke-dasharray="3,3"/>
  <text x="420" y="35" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">1</text>
  
  <circle cx="550" y="30" r="20" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5" stroke-dasharray="3,3"/>
  <text x="550" y="35" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">2</text>
  
  <circle cx="640" y="30" r="20" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5" stroke-dasharray="3,3"/>
  <text x="640" y="35" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">3</text>
  
  <circle cx="720" y="30" r="20" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5" stroke-dasharray="3,3"/>
  <text x="720" y="35" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">4</text>
  
  <circle cx="800" y="30" r="20" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5" stroke-dasharray="3,3"/>
  <text x="800" y="35" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">5</text>
  
  <!-- Vertical separator line (dotted) -->
  <line x1="190" y1="60" x2="190" y2="450" stroke="#5cb3b3" stroke-width="1" stroke-dasharray="5,5"/>
  
  <!-- RAG circle on left -->
  <circle cx="110" y="250" r="80" fill="#c0e0d6" stroke="#6aaa96" stroke-width="2"/>
  <text x="110" y="270" font-family="Arial" font-size="48" text-anchor="middle" font-weight="bold">RAG</text>
  
  <!-- Additional Documents -->
  <rect x="230" y="130" width="140" height="80" fill="white" stroke="#333" stroke-width="1.5"/>
  <text x="300" y="160" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">Additional</text>
  <text x="300" y="185" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">documents</text>
  <!-- Document icon -->
  <rect x="265" y="195" width="70" height="5" fill="black"/>
  <rect x="265" y="205" width="70" height="5" fill="black"/>
  
  <!-- Query Box -->
  <rect x="230" y="350" width="140" height="80" fill="white" stroke="#333" stroke-width="1.5"/>
  <text x="300" y="390" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">Query</text>
  <!-- Pink envelope/query icon -->
  <polygon points="270,410 330,410 330,400 300,390 270,400" fill="#ffccdd" stroke="#333" stroke-width="0.5"/>
  
  <!-- Embedding Model -->
  <rect x="480" y="230" width="150" height="150" rx="20" ry="20" fill="#e6e0f0" stroke="#9f88c9" stroke-width="2"/>
  <text x="555" y="280" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">Embedding</text>
  <text x="555" y="310" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">model</text>
  
  <!-- Neural network icon in embedding model -->
  <path d="M520,340 C530,320 580,320 590,340" stroke="#1a8754" stroke-width="2" fill="none"/>
  <circle cx="525" cy="335" r="4" fill="#1a8754"/>
  <circle cx="540" cy="325" r="4" fill="#1a8754"/>
  <circle cx="555" cy="325" r="4" fill="#1a8754"/>
  <circle cx="570" cy="325" r="4" fill="#1a8754"/>
  <circle cx="585" cy="335" r="4" fill="#1a8754"/>
  
  <!-- Vector Database -->
  <rect x="700" y="150" width="150" height="110" rx="10" ry="10" fill="#e6f4f8" stroke="#42a5c9" stroke-width="2"/>
  <text x="775" y="190" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">Vector</text>
  <text x="775" y="220" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">database</text>
  
  <!-- Database cylinder icon -->
  <ellipse cx="775" cy="160" rx="40" ry="15" fill="#ffcccc" stroke="#cc6666" stroke-width="1"/>
  <rect x="735" y="160" width="80" height="30" fill="#ffcccc" stroke="#cc6666" stroke-width="1"/>
  <ellipse cx="775" cy="190" rx="40" ry="15" fill="#ffcccc" stroke="#cc6666" stroke-width="1"/>
  
  <!-- Similar Documents -->
  <rect x="700" y="380" width="150" height="80" rx="15" ry="15" fill="#fff8e5" stroke="#ffa500" stroke-width="2"/>
  <text x="775" y="410" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">Similar</text>
  <text x="775" y="435" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">documents</text>
  
  <!-- Document icons in similar documents -->
  <rect x="745" y="445" width="20" height="15" fill="white" stroke="#333" stroke-width="0.5"/>
  <line x1="749" y1="450" x2="761" y2="450" stroke="#333" stroke-width="0.5"/>
  <line x1="749" y1="455" x2="761" y2="455" stroke="#333" stroke-width="0.5"/>
  
  <rect x="765" y="445" width="20" height="15" fill="white" stroke="#333" stroke-width="0.5"/>
  <line x1="769" y1="450" x2="781" y2="450" stroke="#333" stroke-width="0.5"/>
  <line x1="769" y1="455" x2="781" y2="455" stroke="#333" stroke-width="0.5"/>
  
  <!-- LLM -->
  <rect x="890" y="380" width="60" height="60" fill="white" stroke="#ff5050" stroke-width="2"/>
  <text x="920" y="415" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">LLM</text>
  
  <!-- Simple neural net for LLM -->
  <path d="M905,425 C915,415 935,415 945,425" stroke="#ff5050" stroke-width="2" fill="none"/>
  
  <!-- Response -->
  <rect x="700" y="480" width="150" height="50" fill="#d9f7d9" stroke="#333" stroke-width="1.5"/>
  <text x="775" y="510" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">Response</text>
  
  <!-- Connection arrows with numbered steps and labels -->
  <!-- Document to Embedding -->
  <text x="380" y="150" font-family="Arial" font-size="14" text-anchor="middle">Encode</text>
  <line x1="370" y1="170" x2="480" y2="250" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="425" y="210" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="425" y="215" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">1</text>
  
  <!-- Query to Embedding -->
  <text x="380" y="350" font-family="Arial" font-size="14" text-anchor="middle">Encode</text>
  <line x1="370" y1="375" x2="480" y2="320" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="425" y="350" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="425" y="355" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">3</text>
  
  <!-- Embedding to Vector DB -->
  <text x="640" y="230" font-family="Arial" font-size="14" text-anchor="middle">Index</text>
  <line x1="630" y1="240" x2="700" y2="205" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="665" y="222" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="665" y="227" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">2</text>
  
  <!-- Vector DB to Similar Docs - Similarity search -->
  <text x="750" y="290" font-family="Arial" font-size="14" text-anchor="middle">Similarity</text>
  <text x="750" y="310" font-family="Arial" font-size="14" text-anchor="middle">search</text>
  <line x1="750" y1="260" x2="750" y2="380" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="750" y="320" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="750" y="325" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">4</text>
  
  <!-- Vector DB to Similar Docs - Similar docs -->
  <text x="800" y="340" font-family="Arial" font-size="14" text-anchor="middle">Similar</text>
  <text x="800" y="360" font-family="Arial" font-size="14" text-anchor="middle">docs</text>
  <line x1="800" y1="260" x2="800" y2="380" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="800" y="320" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="800" y="325" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">5</text>
  
  <!-- Similar Docs to LLM -->
  <text x="865" y="380" font-family="Arial" font-size="14" text-anchor="middle">Prompt</text>
  <line x1="850" y1="400" x2="890" y2="400" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="870" y="400" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="870" y="405" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">6</text>
  
  <!-- LLM to Response -->
  <line x1="850" y1="440" x2="800" y2="480" stroke="#333" stroke-width="1.5" stroke-dasharray="5,5"/>
  <circle cx="825" y="460" r="15" fill="#fff5c7" stroke="#ffd56b" stroke-width="1.5"/>
  <text x="825" y="465" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">7</text>
</svg>