{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"id": "l1BCasjxz6n55HFa06oHR", "type": "rectangle", "x": 517.203125, "y": 280.765625, "width": 138.05859375000003, "height": 48.31640624999999, "angle": 0, "strokeColor": "#846358", "backgroundColor": "#a5d8ff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a0", "roundness": {"type": 3}, "seed": 647852919, "version": 81, "versionNonce": 339454935, "isDeleted": false, "boundElements": [{"id": "y1j23_4IqLobo6UhfByMJ", "type": "arrow"}, {"type": "text", "id": "AkX--qlu1kLwSnHZzdaq_"}], "updated": 1755308622247, "link": null, "locked": false}, {"id": "AkX--qlu1kLwSnHZzdaq_", "type": "text", "x": 559.4724426269531, "y": 292.423828125, "width": 53.51995849609375, "height": 25, "angle": 0, "strokeColor": "#846358", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a0V", "roundness": null, "seed": 1359695929, "version": 18, "versionNonce": 648862937, "isDeleted": false, "boundElements": null, "updated": 1755308633182, "link": null, "locked": false, "text": "Step1", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "l1BCasjxz6n55HFa06oHR", "originalText": "Step1", "autoResize": true, "lineHeight": 1.25}, {"id": "9zIsmxVLDRreuGb9oHzfO", "type": "rectangle", "x": 693.12890625, "y": 280.21484375, "width": 140.92578125, "height": 48.73828125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a1", "roundness": {"type": 3}, "seed": 93823511, "version": 127, "versionNonce": 413892473, "isDeleted": false, "boundElements": [{"id": "y1j23_4IqLobo6UhfByMJ", "type": "arrow"}, {"id": "Gf2jwWb5a0uwnhFkcYqrN", "type": "arrow"}, {"type": "text", "id": "fy3ki7zNXcaDj9FiQPj2N"}], "updated": 1755308636912, "link": null, "locked": false}, {"id": "fy3ki7zNXcaDj9FiQPj2N", "type": "text", "x": 734.1018142700195, "y": 292.083984375, "width": 58.97996520996094, "height": 25, "angle": 0, "strokeColor": "#846358", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a1V", "roundness": null, "seed": 434968439, "version": 11, "versionNonce": 312184791, "isDeleted": false, "boundElements": null, "updated": 1755308641061, "link": null, "locked": false, "text": "Step2", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "9zIsmxVLDRreuGb9oHzfO", "originalText": "Step2", "autoResize": true, "lineHeight": 1.25}, {"id": "iYMUMmmjSWlnpf9Rj6oE2", "type": "rectangle", "x": 876.5390625, "y": 281.77734375, "width": 144.3984375, "height": 48.62890625, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2", "roundness": {"type": 3}, "seed": 125643513, "version": 99, "versionNonce": 722070327, "isDeleted": false, "boundElements": [{"id": "Gf2jwWb5a0uwnhFkcYqrN", "type": "arrow"}, {"id": "Ax7OUfz5-4dy9NYj5n74z", "type": "arrow"}, {"type": "text", "id": "pjIlOl7MvLkGlgDbXj193"}], "updated": 1755308643162, "link": null, "locked": false}, {"id": "pjIlOl7MvLkGlgDbXj193", "type": "text", "x": 920.1683044433594, "y": 293.591796875, "width": 57.13995361328125, "height": 25, "angle": 0, "strokeColor": "#846358", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2V", "roundness": null, "seed": 996903641, "version": 7, "versionNonce": 1733683449, "isDeleted": false, "boundElements": null, "updated": 1755308648039, "link": null, "locked": false, "text": "Step3", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "iYMUMmmjSWlnpf9Rj6oE2", "originalText": "Step3", "autoResize": true, "lineHeight": 1.25}, {"id": "y1j23_4IqLobo6UhfByMJ", "type": "arrow", "x": 657.5970482614534, "y": 302.84308105939857, "width": 34.58947738419329, "height": 0.6355059129477354, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a3", "roundness": {"type": 2}, "seed": 1208839641, "version": 105, "versionNonce": 1736897945, "isDeleted": false, "boundElements": null, "updated": 1755308612093, "link": null, "locked": false, "points": [[0, 0], [34.58947738419329, -0.6355059129477354]], "lastCommittedPoint": null, "startBinding": {"elementId": "l1BCasjxz6n55HFa06oHR", "focus": -0.028983105388209023, "gap": 2.4921875}, "endBinding": {"elementId": "9zIsmxVLDRreuGb9oHzfO", "focus": 0.1404278704261871, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "Gf2jwWb5a0uwnhFkcYqrN", "type": "arrow", "x": 831.76953125, "y": 307.03125, "width": 48.65234375, "height": 0.703125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a4", "roundness": {"type": 2}, "seed": 314337079, "version": 35, "versionNonce": 910291095, "isDeleted": false, "boundElements": null, "updated": 1755308559536, "link": null, "locked": false, "points": [[0, 0], [48.65234375, 0.703125]], "lastCommittedPoint": null, "startBinding": {"elementId": "9zIsmxVLDRreuGb9oHzfO", "focus": 0.05758584913995975, "gap": 2.28515625}, "endBinding": {"elementId": "iYMUMmmjSWlnpf9Rj6oE2", "focus": -0.10371091270942512, "gap": 3.8828125}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "-In45bO_XP_fafNotDtuS", "type": "diamond", "x": 1074.46875, "y": 265.0625, "width": 84.77734375, "height": 81.69140625, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a5", "roundness": {"type": 2}, "seed": 1933247479, "version": 91, "versionNonce": 670257239, "isDeleted": false, "boundElements": [{"id": "Ax7OUfz5-4dy9NYj5n74z", "type": "arrow"}, {"id": "qDfJHtdmzDrsMIVCHUgf3", "type": "arrow"}, {"type": "text", "id": "1SyjiKXV13f15RzHpfpLN"}], "updated": 1755308659227, "link": null, "locked": false}, {"id": "1SyjiKXV13f15RzHpfpLN", "type": "text", "x": 1100.823097229004, "y": 293.4853515625, "width": 31.679977416992188, "height": 25, "angle": 0, "strokeColor": "#846358", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a5G", "roundness": null, "seed": 1088725945, "version": 7, "versionNonce": 764962871, "isDeleted": false, "boundElements": null, "updated": 1755308666644, "link": null, "locked": false, "text": "Yes", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "-In45bO_XP_fafNotDtuS", "originalText": "Yes", "autoResize": true, "lineHeight": 1.25}, {"id": "Ax7OUfz5-4dy9NYj5n74z", "type": "arrow", "x": 1024.5674368296068, "y": 304.6717442670375, "width": 52.04141731921618, "height": 0.6242783936839942, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a6", "roundness": {"type": 2}, "seed": 1693602553, "version": 61, "versionNonce": 1147892695, "isDeleted": false, "boundElements": null, "updated": 1755308657603, "link": null, "locked": false, "points": [[0, 0], [52.04141731921618, -0.6242783936839942]], "lastCommittedPoint": null, "startBinding": {"elementId": "iYMUMmmjSWlnpf9Rj6oE2", "focus": -0.02027307400949034, "gap": 3.87109375}, "endBinding": {"elementId": "-In45bO_XP_fafNotDtuS", "focus": 0.05737378446638313, "gap": 1.0798117100995586}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "qhesFFLlbQLE8fnp4CiEf", "type": "ellipse", "x": 1200.72265625, "y": 274.26953125, "width": 66.7578125, "height": 63.44140625, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a7", "roundness": {"type": 2}, "seed": 854982487, "version": 79, "versionNonce": 1573724055, "isDeleted": false, "boundElements": [{"id": "qDfJHtdmzDrsMIVCHUgf3", "type": "arrow"}, {"type": "text", "id": "4CjQByX9XoLx5B7Z9QTg-"}], "updated": 1755308678481, "link": null, "locked": false}, {"id": "4CjQByX9XoLx5B7Z9QTg-", "type": "text", "x": 1221.6791118472108, "y": 293.5603100913072, "width": 24.639999389648438, "height": 25, "angle": 0, "strokeColor": "#846358", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a7V", "roundness": null, "seed": 1833697401, "version": 6, "versionNonce": 245121623, "isDeleted": false, "boundElements": null, "updated": 1755308680677, "link": null, "locked": false, "text": "No", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "qhesFFLlbQLE8fnp4CiEf", "originalText": "No", "autoResize": true, "lineHeight": 1.25}, {"id": "qDfJHtdmzDrsMIVCHUgf3", "type": "arrow", "x": 1157.072866184087, "y": 307.99090871012163, "width": 42.76952491791326, "height": 0.7278222749461634, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a8", "roundness": {"type": 2}, "seed": 1341144249, "version": 55, "versionNonce": 1352458487, "isDeleted": false, "boundElements": null, "updated": 1755308657603, "link": null, "locked": false, "points": [[0, 0], [42.76952491791326, 0.7278222749461634]], "lastCommittedPoint": null, "startBinding": {"elementId": "-In45bO_XP_fafNotDtuS", "focus": 0.03423135143947463, "gap": 1}, "endBinding": {"elementId": "qhesFFLlbQLE8fnp4CiEf", "focus": -0.10256053996914694, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#ffffff", "lockedMultiSelections": {}}, "files": {}}