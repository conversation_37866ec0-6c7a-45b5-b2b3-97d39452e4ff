{
  "type": "excalidraw",
  "version": 2,
  "source": "Generated by Grok using guidelines",
  "elements": [
    // Top Panel: Linear Flow
    {"id": "title", "type": "text", "x": 50, "y": 20, "width": 400, "height": 25, "text": "AI 辅助绘图生成中主要元素分析方法化", "fontSize": 20, "roughness": 1, "strokeColor": "#1e1e1e"},
    {"id": "box1", "type": "rectangle", "x": 50, "y": 60, "width": 100, "height": 50, "backgroundColor": "#b2f2bb", "roughness": 1, "text": "个性化", "boundElements": [{"type": "arrow", "id": "arrow1"}]},
    {"id": "arrow1", "type": "arrow", "x": 150, "y": 85, "width": 100, "height": 0, "points": [[0,0],[100,0]], "roughness": 1, "startBinding": {"elementId": "box1"}, "endBinding": {"elementId": "box2"}},
    {"id": "box2", "type": "rectangle", "x": 250, "y": 60, "width": 100, "height": 50, "backgroundColor": "#ffc9c9", "roughness": 1, "text": "生成式"},
    {"id": "arrow2", "type": "arrow", "x": 350, "y": 85, "width": 100, "height": 0, "points": [[0,0],[100,0]], "roughness": 1, "startBinding": {"elementId": "box2"}, "endBinding": {"elementId": "box3"}},
    {"id": "box3", "type": "rectangle", "x": 450, "y": 60, "width": 100, "height": 50, "backgroundColor": "#a5d8ff", "roughness": 1, "text": "自动化"},
    
    // Middle Panel: Cloud with Sub-elements
    {"id": "cloud", "type": "ellipse", "x": 200, "y": 150, "width": 200, "height": 100, "backgroundColor": "#a5d8ff", "roughness": 1, "text": "Cloud\nChatGPT ?"},
    {"id": "subbox1", "type": "diamond", "x": 100, "y": 180, "width": 80, "height": 40, "backgroundColor": "#ffec99", "roughness": 1, "text": "子元素"},
    
    // Bottom Panels: Flows with Plus and Icons (simplified; add more as needed)
    {"id": "person1", "type": "ellipse", "x": 50, "y": 300, "width": 50, "height": 50, "backgroundColor": "#ffd43b", "roughness": 1},
    {"id": "plus", "type": "text", "x": 150, "y": 310, "width": 20, "height": 25, "text": "+", "fontSize": 20},
    {"id": "robot", "type": "ellipse", "x": 200, "y": 300, "width": 50, "height": 50, "backgroundColor": "#ff8787", "roughness": 1},
    {"id": "arrow3", "type": "arrow", "x": 250, "y": 325, "width": 100, "height": 0, "points": [[0,0],[100,0]], "roughness": 1}
  ],
  "appState": {
    "viewBackgroundColor": "#ffffff",
    "zoom": {"value": 1},
    "scrollX": 0,
    "scrollY": 0,
    "gridSize": 20
  },
  "files": {}
}