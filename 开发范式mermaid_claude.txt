flowchart TD
    %% Title at top
    classDef titleClass fill:none,stroke:none,color:black,font-size:16px
    title[AI辅助编程给软件工程带来的属于开发范式变化]
    class title titleClass
    
    %% Traditional Development Model
    subgraph trad [" "]
        direction LR
        tradTitle["传统属于<br/>开发模式"] --- tradFlow
        
        subgraph tradFlow [" "]
            direction LR
            t1[需求] --->|"👨‍💼            👨‍💻"| t2[产品设计] 
            t2 ---> t3[开发]
            t3 ---> t4[测试/运维]
        end
        
        class tradTitle tradTitleClass
    end
    
    %% Simple AI Development Model
    subgraph simple [" "]
        direction LR
        simpleTitle["简易属于<br/>开发范式"] --- simpleFlow
        
        subgraph simpleFlow [" "]
            direction LR
            s1[简易需求] -.->|"提取结构化数据"| s2["AI<br/>(ChatGPT/Claude等)"]
            s1 -.->|"识别图片与文字"| s2
            s1 -.->|"翻译、写作、备注"| s2
            
            se["等等... ..."]
        end
        
        class simpleTitle simpleTitleClass
    end
    
    %% Standard AI Development Model
    subgraph standard [" "]
        direction LR
        standardTitle["普通属于<br/>开发范式"] --- standardFlow
        
        subgraph standardFlow [" "]
            direction LR
            st1[普通需求] -.->|"含有PDF"| st2[产品设计]
            st1 -.->|"视频压缩"| st2
            st2 --->|"👨‍💼 👨‍💼 + 🤖"| st3[开发]
            
            ste["等等... ..."]
        end
        
        class standardTitle standardTitleClass
    end
    
    %% Complex AI Development Model
    subgraph complex [" "]
        direction LR
        complexTitle["复杂属于<br/>开发范式"] --- complexFlow
        
        subgraph complexFlow [" "]
            direction LR
            c1[复杂需求] -.->|"OnlyFans"| c2[产品设计]
            c1 -.->|"TikTok"| c2
            c1 -.->|"ChatGPT"| c2
            c2 --->|"👨‍💼 👨‍💻 + 🤖"| c3[开发]
            c3 ---> c4[测试/运维]
            
            ce["等等... ..."]
        end
        
        class complexTitle complexTitleClass
    end
    
    %% Connecting the sections vertically
    title --- trad
    trad --- simple
    simple --- standard
    standard --- complex
    
    %% Styling
    classDef tradTitleClass fill:none,stroke:none,color:#F0A500,font-weight:bold
    classDef simpleTitleClass fill:none,stroke:none,color:#22DD88,font-weight:bold
    classDef standardTitleClass fill:none,stroke:none,color:#8855FF,font-weight:bold
    classDef complexTitleClass fill:none,stroke:none,color:#0088DD,font-weight:bold
    
    classDef default fill:white,stroke:#333,stroke-width:1px,color:black
    classDef requirement fill:#6AD66A,stroke:#333,stroke-width:1px,border-radius:4px
    classDef design fill:#FFDD77,stroke:#333,stroke-width:1px,border-radius:4px
    classDef development fill:#FFAABB,stroke:#333,stroke-width:1px,border-radius:4px
    classDef testing fill:#CCAAFF,stroke:#333,stroke-width:1px,border-radius:4px
    classDef ai fill:#66BBFF,stroke:#333,stroke-width:1px,border-radius:8px
    
    class t1,s1,st1,c1 requirement
    class t2,st2,c2 design
    class t3,st3,c3 development
    class t4,c4 testing
    class s2 ai