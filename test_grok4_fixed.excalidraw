{"type": "excalidraw", "version": 2, "source": "Generated by Grok using guidelines", "elements": [{"id": "title", "type": "text", "x": 50, "y": 20, "width": 400, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1234567890, "version": 1, "versionNonce": 1234567890, "isDeleted": false, "boundElements": null, "updated": 1716335133698, "link": null, "locked": false, "text": "AI 辅助绘图生成中主要元素分析方法化", "fontSize": 20, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 18, "containerId": null, "originalText": "AI 辅助绘图生成中主要元素分析方法化", "lineHeight": 1.25}, {"id": "box1", "type": "rectangle", "x": 50, "y": 60, "width": 100, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1234567891, "version": 1, "versionNonce": 1234567891, "isDeleted": false, "boundElements": [{"type": "text", "id": "box1_text"}, {"id": "arrow1", "type": "arrow"}], "updated": 1716335133698, "link": null, "locked": false}, {"id": "box1_text", "type": "text", "x": 75, "y": 72.5, "width": 50, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1234567892, "version": 1, "versionNonce": 1234567892, "isDeleted": false, "boundElements": null, "updated": 1716335133698, "link": null, "locked": false, "text": "个性化", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "containerId": "box1", "originalText": "个性化", "lineHeight": 1.25}, {"id": "arrow1", "type": "arrow", "x": 150, "y": 85, "width": 100, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1234567893, "version": 1, "versionNonce": 1234567893, "isDeleted": false, "boundElements": null, "updated": 1716335133698, "link": null, "locked": false, "points": [[0, 0], [100, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "box1", "focus": 0, "gap": 0}, "endBinding": {"elementId": "box2", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "box2", "type": "rectangle", "x": 250, "y": 60, "width": 100, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1234567894, "version": 1, "versionNonce": 1234567894, "isDeleted": false, "boundElements": [{"type": "text", "id": "box2_text"}], "updated": 1716335133698, "link": null, "locked": false}, {"id": "box2_text", "type": "text", "x": 275, "y": 72.5, "width": 50, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 1234567895, "version": 1, "versionNonce": 1234567895, "isDeleted": false, "boundElements": null, "updated": 1716335133698, "link": null, "locked": false, "text": "生成式", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "containerId": "box2", "originalText": "生成式", "lineHeight": 1.25}], "appState": {"viewBackgroundColor": "#ffffff", "gridSize": 20}, "files": {}}